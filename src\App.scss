

[data-component='App'] {
  height: 100%;
  width: 100%;
  position: relative;
}
/* App.scss (example) */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.dialog-content {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 20px 25px rgba(0,0,0,0.1);
  max-width: 400px;
  width: 90%;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

/* Example for the advisory chat classes */
.advisory-chat {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 1rem;
}
.chat-header {
  margin-bottom: 1rem;
}
.chat-messages {
  flex: 1;
  border: 1px solid #e2e8f0;
  padding: 1rem;
  border-radius: 8px;
  overflow-y: auto;
  margin-bottom: 1rem;
  max-height: 500px;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.chat-title {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.message-count {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: normal;
}

.chat-controls {
  display: flex;
  gap: 0.5rem;
}

.message-wrapper {
  margin-bottom: 1rem;
}

.chat-message {
  padding: 0.75rem;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.chat-message.user {
  background-color: #e0f2fe;
  border-color: #0284c7;
  margin-left: 2rem;
}

.chat-message.assistant {
  background-color: #f8fafc;
  border-color: #64748b;
  margin-right: 2rem;
}

.message-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.message-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.message-info {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.message-role {
  font-weight: 600;
  font-size: 0.875rem;
  color: #374151;
}

.message-time {
  font-size: 0.75rem;
  color: #6b7280;
}

.message-content {
  line-height: 1.5;
  color: #374151;
}

.message-content p {
  margin: 0 0 0.5rem 0;
}

.message-content p:last-child {
  margin-bottom: 0;
}
.chat-message-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.5rem;
}
.chat-input {
  display: flex;
  gap: 0.5rem;
}
.chat-input input {
  flex: 1;
  padding: 0.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
}
