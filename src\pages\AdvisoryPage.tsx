import { useEffect, useRef, useCallback, useState } from 'react';
import { RealtimeClient } from '@openai/realtime-api-beta';
import { ItemType } from '@openai/realtime-api-beta/dist/lib/client.js';
import { WavRecorder, WavStreamPlayer } from '../lib/wavtools/index.js';
import { X, Zap, MessageSquare, FileText, HelpCircle, Upload } from 'lucide-react';
import { Button } from '../components/button/Button';
import { Toggle } from '../components/toggle/Toggle';
import React from 'react';
import axios from 'axios';
import FileUploader from '../components/FileUploader';
import './AdvisoryPage.scss';

const LOCAL_RELAY_SERVER_URL: string = process.env.REACT_APP_LOCAL_RELAY_SERVER_URL || '';

// Drilling expert assistant instructions
const instructions = `
You are an expert drilling operations assistant specialized in:
- Directional drilling techniques and problem-solving
- Well logging interpretation and best practices
- Completion operations and equipment selection
- Tubing running services and installation procedures
- Mud and fluid engineering for various formation types
- Downhole equipment troubleshooting and selection
- Fishing operations and stuck pipe recovery techniques
- Formation issues including lost circulation, wellbore instability
- General advisory for drilling supervisors during service execution

Your role is to provide expert advice, answer questions, suggest solutions to common drilling problems, 
and analyze technical data from uploaded files. Think of yourself as a seasoned drilling consultant 
with decades of field experience that a Drilling Supervisor can turn to for guidance.

When responding to questions about uploaded files, first analyze the content, then provide insights
and recommendations based on the information available.
`;

// Interface definitions
interface RealtimeEvent {
  time: string;
  source: 'client' | 'server';
  count?: number;
  event: { [key: string]: any };
}

// Main component
export function AdvisoryPage(): JSX.Element {
  // API Key setup
  const apiKey = LOCAL_RELAY_SERVER_URL
    ? ''
    : localStorage.getItem('tmp::voice_api_key') || prompt('OpenAI API Key') || '';
  if (apiKey !== '') {
    localStorage.setItem('tmp::voice_api_key', apiKey);
  }

  // Core refs
  const wavRecorderRef = useRef<WavRecorder>(new WavRecorder({ sampleRate: 24000 }));
  const wavStreamPlayerRef = useRef<WavStreamPlayer>(
    new WavStreamPlayer({ sampleRate: 24000 })
  );
  const clientRef = useRef<RealtimeClient>(
    new RealtimeClient(
      LOCAL_RELAY_SERVER_URL
        ? { url: LOCAL_RELAY_SERVER_URL }
        : {
            apiKey: apiKey,
            dangerouslyAllowAPIKeyInBrowser: true,
          }
    )
  );

  // UI refs
  const clientCanvasRef = useRef<HTMLCanvasElement>(null);
  const serverCanvasRef = useRef<HTMLCanvasElement>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const startTimeRef = useRef<string>(new Date().toISOString());

  // State
  const [items, setItems] = useState<ItemType[]>([]);
  const [realtimeEvents, setRealtimeEvents] = useState<RealtimeEvent[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [canPushToTalk, setCanPushToTalk] = useState(true);
  const [isRecording, setIsRecording] = useState(false);
  const [chatMessages, setChatMessages] = useState<{role: string, content: string}[]>([]);
  const [inputText, setInputText] = useState('');

  // Function to fetch file content - IDENTICAL to original code
  const fetchUploadedFileContent = async (): Promise<string> => {
    try {
      const response = await axios.get('http://localhost:3001/file-content');
      console.log('Fetched file content successfully');
      return response.data.content;
    } catch (error) {
      console.error('Error fetching uploaded file content:', error);
      return 'Error fetching file content';
    }
  };

  // Start conversation with file content
  
  const startConversationWithFileContent = async () => {
    if (!isConnected) {
      await connectConversation();
    }
  
    try {
      const fileContent = await fetchUploadedFileContent();
      console.log('File content length:', fileContent.length);
      
      // Force-add the user message to the chat first
      const userMessage = {
        role: 'user',
        content: 'Please analyze the uploaded files as a drilling expert.'
      };
      
      setChatMessages(prev => [...prev, userMessage]);
      
      // Then send to AI
      clientRef.current.sendUserMessageContent([
        {
          type: 'input_text',
          text: `Uploaded files content:\n${fileContent}\n\nPlease analyze this data and provide insights, recommendations, or identify any issues as a drilling expert.`,
        },
      ]);
    } catch (error) {
      console.error('Error starting conversation with file content:', error);
    }
  };


  // Connection management - MATCHING original approach
  const connectConversation = useCallback(async () => {
    const client = clientRef.current;
    const wavRecorder = wavRecorderRef.current;
    const wavStreamPlayer = wavStreamPlayerRef.current;
  
    startTimeRef.current = new Date().toISOString();
    setIsConnected(true);
    setRealtimeEvents([]);
    setItems(client.conversation.getItems());
  
    await wavRecorder.begin();
    await wavStreamPlayer.connect();
    await client.connect();
  
    // Get uploaded content - this matches the original code
    const uploadedContent = await fetchUploadedFileContent();
    console.log('Retrieved uploaded content for initial setup');
  
    // Update session with file content - this is crucial for proper file access
    client.updateSession({ 
      instructions: `${instructions}\n\nUploaded File Content:\n${uploadedContent}` 
    });
    
    client.updateSession({ 
      input_audio_transcription: { model: 'whisper-1' }
    });
  
    // Send initial message with file content - this is hidden from chat display
    client.sendUserMessageContent([
      {
        type: 'input_text',
        text: `Hello! I've uploaded files with the following content:\n${uploadedContent}\nFeel free to ask me questions as a drilling expert.`,
      },
    ]);
  
    if (client.getTurnDetectionType() === 'server_vad') {
      await wavRecorder.record((data) => client.appendInputAudio(data.mono));
    }
    
    // Welcome message - ADD to existing messages instead of replacing
    setChatMessages(prev => [...prev, {
      role: 'assistant',
      content: 'Hello! I am your drilling operations expert assistant. I can help with directional drilling, logging, completion, tubing services, mud engineering, downhole equipment, fishing operations, and drilling problem-solving. How can I assist you today?'
    }]);
  }, []);

  const disconnectConversation = useCallback(async () => {
    setIsConnected(false);
    setRealtimeEvents([]);
    setItems([]);
    // Note: We're NOT clearing chatMessages here to preserve conversation history

    const client = clientRef.current;
    client.disconnect();

    const wavRecorder = wavRecorderRef.current;
    await wavRecorder.end();

    const wavStreamPlayer = wavStreamPlayerRef.current;
    await wavStreamPlayer.interrupt();
  }, []);

  // Function to clear chat history
  const clearChatHistory = useCallback(() => {
    setChatMessages([]);
  }, []);

  // Reset API key
  const resetAPIKey = useCallback(() => {
    const apiKey = prompt('OpenAI API Key');
    if (apiKey !== null) {
      localStorage.clear();
      localStorage.setItem('tmp::voice_api_key', apiKey);
      window.location.reload();
    }
  }, []);

  // Turn detection mode
  const changeTurnEndType = async (value: string) => {
    const client = clientRef.current;
    const wavRecorder = wavRecorderRef.current;
    if (value === 'none' && wavRecorder.getStatus() === 'recording') {
      await wavRecorder.pause();
    }
    client.updateSession({
      turn_detection: value === 'none' ? null : { type: 'server_vad' },
    });
    if (value === 'server_vad' && client.isConnected()) {
      await wavRecorder.record((data) => client.appendInputAudio(data.mono));
    }
    setCanPushToTalk(value === 'none');
  };

  // Recording controls
  const startRecording = async () => {
    try {
      const client = clientRef.current;
      const wavRecorder = wavRecorderRef.current;
      const wavStreamPlayer = wavStreamPlayerRef.current;

      if (wavRecorder.getStatus() === 'recording') {
        await wavRecorder.pause();
      }

      setIsRecording(true);

      const trackSampleOffset = await wavStreamPlayer.interrupt();
      if (trackSampleOffset?.trackId) {
        const { trackId, offset } = trackSampleOffset;
        await client.cancelResponse(trackId, offset);
      }

      await wavRecorder.record((data) => client.appendInputAudio(data.mono));
    } catch (error) {
      console.error('Error starting recording:', error);
      setIsRecording(false);
    }
  };
  
  const stopRecording = async () => {
    try {
      const client = clientRef.current;
      const wavRecorder = wavRecorderRef.current;

      setIsRecording(false);
      await wavRecorder.pause();
      client.createResponse();
    } catch (error) {
      console.error('Error stopping recording:', error);
    }
  };

  // Add this function to test basic message display
const testAddMessage = () => {
  setChatMessages(prev => [
    ...prev,
    { role: 'assistant', content: 'This is a test message from the assistant.' }
  ]);
  console.log('Test message added');
};

  // Handle text input submission
  const handleInputSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!inputText.trim() || !isConnected) return;
    
    // Send message to the AI
    clientRef.current.sendUserMessageContent([
      {
        type: 'input_text',
        text: inputText,
      },
    ]);
    
    // Add to chat messages
    setChatMessages(prev => [
      ...prev,
      { role: 'user', content: inputText }
    ]);
    
    // Clear input
    setInputText('');
  };

  // Format chat message for display
  const formatChatMessage = (message: {role: string, content: string}, index: number) => {
    const timestamp = new Date().toLocaleTimeString();

    return (
      <div className={`chat-message ${message.role}`}>
        <div className="message-header">
          <div className="message-avatar">
            {message.role === 'assistant' ? (
              <HelpCircle size={20} />
            ) : message.role === 'system' ? (
              <FileText size={20} />
            ) : (
              <MessageSquare size={20} />
            )}
          </div>
          <div className="message-info">
            <span className="message-role">
              {message.role === 'assistant' ? 'Drilling Expert' : 'You'}
            </span>
            <span className="message-time">{timestamp}</span>
          </div>
        </div>
        <div className="message-content">
          {message.content.split('\n').map((line, i) => (
            <p key={i}>{line || ' '}</p>
          ))}
        </div>
      </div>
    );
  };

  // Scroll to the bottom of chat when messages change
  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  }, [chatMessages]);

  // API and client setup
  useEffect(() => {
    const client = clientRef.current;
    const wavStreamPlayer = wavStreamPlayerRef.current;
    
    // Basic canvas setup
    if (clientCanvasRef.current) {
      const ctx = clientCanvasRef.current.getContext('2d');
      if (ctx) {
        ctx.fillStyle = 'rgb(0, 112, 240)';
        ctx.fillRect(0, 0, clientCanvasRef.current.width, clientCanvasRef.current.height);
      }
    }
    
    if (serverCanvasRef.current) {
      const ctx = serverCanvasRef.current.getContext('2d');
      if (ctx) {
        ctx.fillStyle = 'rgb(240, 112, 0)';
        ctx.fillRect(0, 0, serverCanvasRef.current.width, serverCanvasRef.current.height);
      }
    }
  
    // Event handlers
    client.on('realtime.event', (realtimeEvent: RealtimeEvent) => {
      console.log('Realtime event:', realtimeEvent);
      setRealtimeEvents((events) => {
        const lastEvent = events[events.length - 1];
        if (lastEvent?.event.type === realtimeEvent.event.type) {
          lastEvent.count = (lastEvent.count || 0) + 1;
          return [...events.slice(0, -1), lastEvent];
        }
        return [...events, realtimeEvent];
      });
    });

    client.on('error', (error: Error) => {
      console.error('Client error:', error);
    });

    client.on('conversation.interrupted', async () => {
      console.log('Conversation interrupted');
      const trackSampleOffset = await wavStreamPlayer.interrupt();
      if (trackSampleOffset?.trackId) {
        const { trackId, offset } = trackSampleOffset;
        await client.cancelResponse(trackId, offset);
      }
    });

    // UPDATED: Improved handling of conversation updates
   
// Replace your current conversation.updated handler with this one

client.on('conversation.updated', ({ item, delta }: { item: any; delta: any }) => {
  console.log('Conversation updated:', { itemRole: item.role, itemId: item.id, delta });

  // Process audio
  if (delta?.audio) {
    wavStreamPlayer.add16BitPCM(delta.audio, item.id);
  }

  // Handle streaming text content (delta updates)
  if (delta?.content && delta.content.text) {
    console.log('Streaming text delta:', delta.content.text);

    setChatMessages(prevMessages => {
      const lastMessage = prevMessages[prevMessages.length - 1];

      // If last message is from assistant and we're getting more content, append it
      if (lastMessage && lastMessage.role === 'assistant' && item.role === 'assistant') {
        const newMessages = [...prevMessages];
        newMessages[newMessages.length - 1] = {
          ...lastMessage,
          content: lastMessage.content + delta.content.text
        };
        return newMessages;
      } else if (item.role === 'assistant') {
        // Start a new assistant message
        return [...prevMessages, { role: 'assistant', content: delta.content.text }];
      }

      return prevMessages;
    });
  }

  // Handle complete messages (when item has full content)
  if (item.content && item.content.text && !delta?.content) {
    console.log('Complete message:', item.role, item.content.text);

    // Skip the initial system message that contains file content
    const isInitialFileMessage = item.role === 'user' &&
      item.content.text.includes('I\'ve uploaded files with the following content:');

    if (!isInitialFileMessage) {
      setChatMessages(prevMessages => {
        // Check if this exact message already exists
        const messageExists = prevMessages.some(msg =>
          msg.role === item.role &&
          msg.content === item.content.text
        );

        if (!messageExists) {
          return [...prevMessages, { role: item.role, content: item.content.text }];
        }

        return prevMessages;
      });
    }
  }

  // Handle audio transcription for user messages
  if (item.role === 'user' && item.content && item.content.input_audio_transcription) {
    console.log('User audio transcription:', item.content.input_audio_transcription.transcript);

    setChatMessages(prevMessages => {
      const transcript = item.content.input_audio_transcription.transcript;
      const messageExists = prevMessages.some(msg =>
        msg.role === 'user' && msg.content === transcript
      );

      if (!messageExists && transcript.trim()) {
        return [...prevMessages, { role: 'user', content: transcript }];
      }

      return prevMessages;
    });
  }

  setItems(client.conversation.getItems());
});

    return () => {
      client.reset();
    };
  }, []);

  // Render method
  return (
    <div data-component="AdvisoryPage">
      <div className="header">
        <div className="header-title">
          <span>Drilling Expert Advisory System</span>
        </div>
        <div className="header-action">
          {!LOCAL_RELAY_SERVER_URL && (
            <Button
              buttonStyle="flush"
              label={`API Key: ${apiKey.slice(0, 3)}...`}
              onClick={resetAPIKey}
            />
          )}
        </div>
      </div>

      <div className="main-container">
        <div className="sidebar">
          <div className="file-section">
            <h3>Drilling Files</h3>
            
            <FileUploader />
            
            <div className="file-actions">
              <Button 
                icon={FileText}
                label="Analyze Uploaded Files" 
                onClick={startConversationWithFileContent}
                buttonStyle="action"
              />
            </div>
          </div>
          
          <div className="voice-controls">
            <h3>Voice Conversation</h3>
            <div className="voice-toggle">
              <label>Mode:</label>
              <Toggle
                defaultValue={false}
                labels={['Manual', 'Voice Detection']}
                values={['none', 'server_vad']}
                onChange={(_, value) => changeTurnEndType(value)}
              />
            </div>

            {isConnected && canPushToTalk && (
              <Button
                label={isRecording ? 'Release to Send' : 'Push to Talk'}
                buttonStyle={isRecording ? 'alert' : 'regular'}
                disabled={!isConnected || !canPushToTalk}
                onMouseDown={startRecording}
                onMouseUp={stopRecording}
                className="talk-button"
              />
            )}

            <Button
              label={isConnected ? 'Disconnect' : 'Connect to Assistant'}
              iconPosition={isConnected ? 'end' : 'start'}
              icon={isConnected ? X : Zap}
              buttonStyle={isConnected ? 'regular' : 'action'}
              onClick={isConnected ? disconnectConversation : connectConversation}
              className="connect-button"
            />

            <div className="visualization">
              <canvas ref={clientCanvasRef} className="visualization-canvas" />
              <canvas ref={serverCanvasRef} className="visualization-canvas" />
            </div>
          </div>

          <div className="expertise-list">
            <h3>Drilling Expertise</h3>
            <ul>
              <li>Directional Drilling</li>
              <li>Well Logging</li>
              <li>Completion Operations</li>
              <li>Tubing Running Services</li>
              <li>Mud & Fluid Engineering</li>
              <li>Downhole Equipment</li>
              <li>Fishing Operations</li>
              <li>Formation Issues</li>
              <li>Drilling Problem-Solving</li>
            </ul>
          </div>
        </div>

        <div className="chat-container">
          <div className="chat-header">
            <div className="chat-title">
              <h2>
                <HelpCircle size={24} />
                Drilling Expert Conversation
              </h2>
              <span className="message-count">
                {chatMessages.length} message{chatMessages.length !== 1 ? 's' : ''}
              </span>
            </div>
            <div className="chat-controls">
              <Button
                label="Clear Chat"
                buttonStyle="flush"
                onClick={clearChatHistory}
                disabled={chatMessages.length === 0}
              />
            </div>
          </div>
          
          <div className="chat-messages" ref={chatContainerRef}>
            {chatMessages.length > 0 ? (
            chatMessages.map((msg, index) => (
              <div key={index} className="message-wrapper">
                {formatChatMessage(msg, index)}
            </div>
    ))
  ) : (
              <div className="empty-chat">
                <div className="empty-state-icon">
                  <MessageSquare size={48} />
                </div>
                <h3>Ready to assist with drilling operations</h3>
                <p>Connect to start a conversation with your drilling operations expert.</p>
                <p>You can ask questions about directional drilling, well problems, fishing operations, and more.</p>
                <Button
                  label="Connect to Assistant"
                  icon={Zap}
                  buttonStyle="action"
                  onClick={connectConversation}
                />
              </div>
            )}
          </div>
          
          <div className="chat-input">
            <form onSubmit={handleInputSubmit}>
              <input
                type="text"
                placeholder={isConnected ? "Type your question here..." : "Connect to ask questions..."}
                value={inputText}
                onChange={(e) => setInputText(e.target.value)}
                disabled={!isConnected}
              />
              <Button
                type="submit"
                buttonStyle="action"
                icon={MessageSquare}
                disabled={!isConnected || !inputText.trim()}
              />
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdvisoryPage;