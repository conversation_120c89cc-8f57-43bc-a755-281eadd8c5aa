{"name": "dingbat-to-unicode", "version": "1.0.1", "description": "Mapping from Dingbat fonts, such as Symbol, Webdings and Wingdings, to Unicode code points", "main": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist"], "scripts": {"prepare": "tsc", "test": "mocha --ui exports --require ts-node/register 'test/**/*.ts'"}, "repository": {"type": "git", "url": "git+https://github.com/mwilliamson/dingbat-to-unicode.git"}, "keywords": ["dingbat", "dingbats", "symbol", "webdings", "wingdings", "unicode"], "author": "<PERSON> <<EMAIL>>", "license": "BSD-2-<PERSON><PERSON>", "bugs": {"url": "https://github.com/mwilliamson/dingbat-to-unicode/issues"}, "homepage": "https://github.com/mwilliamson/dingbat-to-unicode#readme", "devDependencies": {"@types/mocha": "^8.2.0", "@types/node": "^14.14.21", "mocha": "^8.2.1", "ts-node": "^9.1.1", "typescript": "^4.1.3"}}